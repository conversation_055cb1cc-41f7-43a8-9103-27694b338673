/**
 * 空节点
 */
import { Msg } from "../../../proto/msg-define"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import PlanetNodeModel from "./PlanetNodeModel"

type shadowData = {
    x: number,
    y: number
}

export default class PlanetEmptyNode extends PlanetNodeModel {
    public customFun: Function = null

    public json: any = null
    public timeStoneShadow: shadowData[] = []

    public nodeRewards: number[] = [] //节点已领取的奖励
    public get rewardPoints(): number[] { //第x个奖励所在的位置
        return this.json?.rewardPoints || []
    }
    public get prefab() {
        return this.json?.prefab || this.eventName
    }

    public init(nodeId, ...params) {
        super.init(nodeId)
        this.initJson()
        this.initReawrds()
        return this
    }

    protected initJson() {
        let name = "ChapterPlanetSp"
        if (this.map.getBranch()) {
            name = "BranchPlanetSp"
        }
        this.json = assetsMgr.checkJsonData(name, this.id)
    }

    //领取节点内部的奖励
    public async claimNodeReward(index: number) {
        let map = this.map
        let branch = map.getBranch()
        let data
        if (branch) {
            data = await gameHelper.net.requestWithDataWait(Msg.C2S_ClaimBranchPlanetNodeRewardMessage, { branchId: branch.id, mapId: map.getId(), nodeId: this.index, index })
        }
        else {
            return false
        }

        let code = data.code
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }

        this.nodeRewards.push(index)

        let reward = this.rewards[index]
        if (reward) {
            gameHelper.grantReward(reward)
        }

        return true
    }

    public async die() {
        let succ = await this.syncDie()
        if (succ) {
            if (this.dead) return succ
            this.dead = true
            gameHelper.grantRewards(this.getRestRewards())
            this.map.nextNode()
        }
        return succ
    }

    public checkRewardByPoint(point: number) {
        let index = this.getRewardIndex(point)
        if (index < 0) return false
        return !this.nodeRewards.has(index)
    }

    public getRewardIndex(point: number) {
        return this.rewardPoints.indexOf(point)
    }

    public getRestRewards() {
        return this.rewards.filter((_, index) => !this.nodeRewards.has(index))
    }

    public toDB() {
        return {
            id: this.id,
            progress: this.progress,
            shadow: this.timeStoneShadow
        }
    }

    public fromDB(data: any) {
        this.progress = this.progress || data.progress || 0
        this.timeStoneShadow = data.shadow || []
    }

    public pushShadow(data: shadowData) {
        this.timeStoneShadow.push(data)
    }

}
